package dto

import "socks/server/domain/entity"

// ClientFilterRequest 客户端筛选请求
type ClientFilterRequest struct {
	Name          string `json:"name,omitempty"`           // 客户端名称，空字符串表示不筛选此字段
	IP            string `json:"ip,omitempty"`             // 客户端IP地址，空字符串表示不筛选此字段
	UUID          string `json:"uuid,omitempty"`           // 客户端唯一id，空字符串表示不筛选此字段
	Type          string `json:"type,omitempty"`           // 端口对应的代理，空字符串表示不筛选此字段
	Group         string `json:"group,omitempty"`          // 代理客户端所在集群组别，空字符串表示不筛选此字段
	RelayVersion  string `json:"relay_version,omitempty"`  // Relay版本，空字符串表示不筛选此字段
	ClientVersion string `json:"client_version,omitempty"` // 客户端版本，空字符串表示不筛选此字段
}

// ClientFilterResponse 客户端筛选响应
type ClientFilterResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message,omitempty"`
	Data    []ClientInfo `json:"data,omitempty"`
	Total   int          `json:"total"`
}

// ClientInfo 客户端信息
type ClientInfo struct {
	Name          string `json:"name"`           // 客户端名称
	IP            string `json:"ip"`             // 客户端IP地址
	UUID          string `json:"uuid"`           // 客户端唯一id
	Type          string `json:"type"`           // 端口对应的代理
	Group         string `json:"group"`          // 代理客户端所在集群组别
	RelayVersion  string `json:"relay_version"`  // Relay版本
	ClientVersion string `json:"client_version"` // 客户端版本
}

// ToClientInfo 将 entity.Client 转换为 ClientInfo
func ToClientInfo(client *entity.Client) ClientInfo {
	return ClientInfo{
		Name:          client.Name,
		IP:            client.IP,
		UUID:          client.UUID,
		Type:          client.Type,
		Group:         client.Group,
		RelayVersion:  client.RelayVersion,
		ClientVersion: client.ClientVersion,
	}
}

// ToClientInfoList 将 entity.Client 列表转换为 ClientInfo 列表
func ToClientInfoList(clients []*entity.Client) []ClientInfo {
	result := make([]ClientInfo, len(clients))
	for i, client := range clients {
		result[i] = ToClientInfo(client)
	}
	return result
}

// GroupInfo 组信息
type GroupInfo struct {
	Group   string       `json:"group"`
	Clients []ClientInfo `json:"clients"`
}

// ClientGroupResponse 按组分组的客户端响应
type ClientGroupResponse struct {
	Data []GroupInfo `json:"data"`
}

// ToClientGroupResponse 将按组分组的客户端数据转换为响应格式
func ToClientGroupResponse(clientsByGroup map[string][]*entity.Client) ClientGroupResponse {
	data := make([]GroupInfo, 0, len(clientsByGroup))

	for group, clients := range clientsByGroup {
		groupInfo := GroupInfo{
			Group:   group,
			Clients: ToClientInfoList(clients),
		}
		data = append(data, groupInfo)
	}

	return ClientGroupResponse{
		Data: data,
	}
}
