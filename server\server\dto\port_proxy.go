package dto

import (
	common "socks/relay/common"
	"socks/server/domain/entity"
)

func NewClient(clientId, clientIp, clientName, clientType, clientGroup string) *entity.Client {
	return &entity.Client{
		UUID:  clientId,
		IP:    clientIp,
		Name:  clientName,
		Type:  clientType,
		Group: clientGroup,
	}
}

func NewClientWithVersions(clientId, clientIp, clientName, clientType, clientGroup, relayVersion, clientVersion string) *entity.Client {
	if !common.IsTopicExist(clientType) {
		clientType = common.GetDefaultTopic()
	}
	return &entity.Client{
		UUID:          clientId,
		IP:            clientIp,
		Name:          clientName,
		Type:          clientType,
		Group:         clientGroup,
		RelayVersion:  relayVersion,
		ClientVersion: clientVersion,
	}
}
