package relay

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
)

type ProtoType uint8
type MessageType uint8

const (
	ProtoTCP      ProtoType = 0x01
	ProtoTCP4     ProtoType = 0x02
	ProtoTCP6     ProtoType = 0x03
	ProtoUDP      ProtoType = 0x04
	ProtoUDP4     ProtoType = 0x05
	ProtoUDP6     ProtoType = 0x06
	ProtoIP       ProtoType = 0x07
	ProtoIP4      ProtoType = 0x08
	ProtoIP6      ProtoType = 0x09
	ProtoUNIX     ProtoType = 0x0A
	ProtoUNIXGRAM ProtoType = 0x0B
	ProtoUNIXPACK ProtoType = 0x0C
	ProtoHTTP     ProtoType = 0x0D // HTTP代理类型

	MessageTypeRequest   MessageType = 0x01 // 请求消息
	MessageTypeConfirm   MessageType = 0x02 // 确认消息
	MessageTypeData      MessageType = 0x03 // 数据消息
	MessageTypeClose     MessageType = 0x04 // 关闭消息
	MessageTypeHeartbeat MessageType = 0x05 // 心跳消息

	Version uint8 = 0x01
)

var protoToNetwork = map[ProtoType]string{
	ProtoTCP:      "tcp",
	ProtoTCP4:     "tcp4",
	ProtoTCP6:     "tcp6",
	ProtoUDP:      "udp",
	ProtoUDP4:     "udp4",
	ProtoUDP6:     "udp6",
	ProtoIP:       "ip",
	ProtoIP4:      "ip4",
	ProtoIP6:      "ip6",
	ProtoUNIX:     "unix",
	ProtoUNIXGRAM: "unixgram",
	ProtoUNIXPACK: "unixpacket",
	ProtoHTTP:     "http", // HTTP代理使用tcp连接
}

type RequestMessage struct {
	Version uint8
	Proto   ProtoType
	Address string
}

// ResponseMessage 确认消息结构
type ResponseMessage struct {
	Version uint8
	Type    MessageType
	Success bool   // 是否成功
	Message string // 错误信息或成功信息
}

// 编码请求
func (r *RequestMessage) Encode() ([]byte, error) {
	buf := new(bytes.Buffer)

	buf.WriteByte(r.Version)
	buf.WriteByte(uint8(r.Proto))

	addrBytes := []byte(r.Address)
	addrLen := uint16(len(addrBytes))
	if err := binary.Write(buf, binary.BigEndian, addrLen); err != nil {
		return nil, err
	}
	buf.Write(addrBytes)

	return buf.Bytes(), nil
}

func (r *RequestMessage) String() string {
	return fmt.Sprintf("Version=%d, Proto=%s, Addr=%s", r.Version, protoToNetwork[r.Proto], r.Address)
}

// 解码请求
func DecodeRequestMessage(r io.Reader) (*RequestMessage, error) {
	header := make([]byte, 4)
	if _, err := io.ReadFull(r, header); err != nil {
		return nil, err
	}

	req := &RequestMessage{}
	req.Version = header[0]
	if req.Version != Version {
		return nil, fmt.Errorf("unsupported version: %d", req.Version)
	}
	req.Proto = ProtoType(header[1])
	addrLen := binary.BigEndian.Uint16(header[2:4])

	addrBytes := make([]byte, addrLen)
	if _, err := io.ReadFull(r, addrBytes); err != nil {
		return nil, err
	}
	req.Address = string(addrBytes)

	return req, nil
}

func NewRequestMessage(proto ProtoType, address string) *RequestMessage {
	return &RequestMessage{Version: Version, Proto: proto, Address: address}
}

// 编码确认消息
func (c *ResponseMessage) Encode() ([]byte, error) {
	buf := new(bytes.Buffer)

	buf.WriteByte(c.Version)
	buf.WriteByte(uint8(c.Type))

	if c.Success {
		buf.WriteByte(0x01)
	} else {
		buf.WriteByte(0x00)
	}

	msgBytes := []byte(c.Message)
	msgLen := uint16(len(msgBytes))
	if err := binary.Write(buf, binary.BigEndian, msgLen); err != nil {
		return nil, err
	}
	buf.Write(msgBytes)

	return buf.Bytes(), nil
}

// 解码确认消息
func DecodeConfirmMessage(r io.Reader) (*ResponseMessage, error) {
	header := make([]byte, 5)
	if _, err := io.ReadFull(r, header); err != nil {
		return nil, err
	}

	confirm := &ResponseMessage{}
	confirm.Version = header[0]
	if confirm.Version != Version {
		return nil, fmt.Errorf("unsupported version: %d", confirm.Version)
	}
	confirm.Type = MessageType(header[1])
	confirm.Success = header[2] == 0x01
	msgLen := binary.BigEndian.Uint16(header[3:5])

	if msgLen > 0 {
		msgBytes := make([]byte, msgLen)
		if _, err := io.ReadFull(r, msgBytes); err != nil {
			return nil, err
		}
		confirm.Message = string(msgBytes)
	}

	return confirm, nil
}

// NewConfirmMessage 创建确认消息
func NewConfirmResponseMessage(success bool, message string) *ResponseMessage {
	return &ResponseMessage{
		Version: Version,
		Type:    MessageTypeConfirm,
		Success: success,
		Message: message,
	}
}
