#!/bin/bash

# 隧道系统测试运行脚本
# Linux/Unix 脚本用于运行各种测试
go env -w GO111MODULE=on
go env -w GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
set -e  # 遇到错误时退出

echo "=== 隧道系统集成测试 ==="
echo

# 检查当前目录
if [ ! -f "tunnel_test.go" ]; then
    echo "错误: 请在 test 目录下运行此脚本"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数：运行测试并显示结果
run_test() {
    local test_name="$1"
    local command="$2"
    local description="$3"
    
    echo -e "${YELLOW}--- $test_name ---${NC}"
    echo "描述: $description"
    echo "命令: $command"
    echo
    
    start_time=$(date +%s.%N)
    
    if eval "$command"; then
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc -l)
        printf "${GREEN}✅ %s 通过 (耗时: %.2f秒)${NC}\n" "$test_name" "$duration"
    else
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc -l)
        printf "${RED}❌ %s 失败 (耗时: %.2f秒)${NC}\n" "$test_name" "$duration"
    fi
    echo
}

# 1. 运行基础集成测试
run_test "基础集成测试" \
    "go test -v -run TestTunnelIntegration" \
    "测试配置加载、数据库连接、客户端注册等基础功能"

# 2. 运行客户端服务端集成测试
run_test "客户端服务端集成测试" \
    "go test -v -run TestClientServerIntegration" \
    "测试客户端和服务端配置文件集成"

# 3. 运行所有基础测试
run_test "所有基础测试" \
    "go test -v" \
    "运行所有不需要 CGO 的测试"

# 4. 检查是否支持 CGO 和 SQLite3
echo -e "${YELLOW}--- 检查 CGO 和 SQLite3 支持 ---${NC}"
echo "正在检查 CGO 支持..."

# 检查是否安装了 gcc
if command -v gcc >/dev/null 2>&1; then
    echo -e "${GREEN}✅ GCC 编译器可用${NC}"
    
    # 检查是否安装了 sqlite3 开发库
    if pkg-config --exists sqlite3 2>/dev/null || [ -f /usr/include/sqlite3.h ] || [ -f /usr/local/include/sqlite3.h ]; then
        echo -e "${GREEN}✅ SQLite3 开发库可用${NC}"
        
        # 尝试运行 SQLite3 测试
        echo "正在尝试运行 SQLite3 测试..."
        run_test "SQLite3 集成测试" \
            "CGO_ENABLED=1 go test -v -run TestTunnelIntegrationWithSQLite -tags cgo" \
            "测试 SQLite3 数据库功能"
    else
        echo -e "${YELLOW}⚠️  SQLite3 开发库不可用，跳过 SQLite3 测试${NC}"
        echo "如需运行 SQLite3 测试，请安装 SQLite3 开发库:"
        echo "  Ubuntu/Debian: sudo apt-get install libsqlite3-dev"
        echo "  CentOS/RHEL:   sudo yum install sqlite-devel"
        echo "  Alpine:        sudo apk add sqlite-dev"
    fi
else
    echo -e "${YELLOW}⚠️  GCC 编译器不可用，跳过 SQLite3 测试${NC}"
    echo "如需运行 SQLite3 测试，请安装 GCC 编译器:"
    echo "  Ubuntu/Debian: sudo apt-get install build-essential"
    echo "  CentOS/RHEL:   sudo yum groupinstall 'Development Tools'"
    echo "  Alpine:        sudo apk add build-base"
fi

echo
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo
echo "测试说明:"
echo "- 基础测试使用内存数据库，无需外部依赖"
echo "- SQLite3 测试需要 CGO 支持和 GCC 编译器"
echo "- 所有测试使用不同端口范围避免冲突"
echo
echo "如需查看详细说明，请参考 README.md 文件"

# 显示系统信息
echo
echo -e "${YELLOW}=== 系统信息 ===${NC}"
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo "Go 版本: $(go version 2>/dev/null || echo '未安装')"
if command -v gcc >/dev/null 2>&1; then
    echo "GCC 版本: $(gcc --version | head -n1)"
else
    echo "GCC: 未安装"
fi
